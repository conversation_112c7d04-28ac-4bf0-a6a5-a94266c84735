"""
title: DeepSeek R1
author: laolilin1
description: 转接昇腾mindie部署deepseek
version: 0.0.1
licence: MIT
reference: https://openwebui.com/f/zgccrui/deepseek_r1
"""

import asyncio
import json
import re
import traceback
from collections.abc import AsyncGenerator, Awaitable, Callable
from typing import Any

import httpx
from pydantic import BaseModel, Field


class Pipe:
    class Valves(BaseModel):
        DEEPSEEK_API_BASE_URL: str = Field(
            default="https://api.deepseek.com/v1",
            description="DeepSeek API的基础请求地址",
        )
        DEEPSEEK_API_KEY: str = Field(default="", description="用于身份验证的DeepSeek API密钥，可从控制台获取")
        DEEPSEEK_API_MODEL: str = Field(
            default="deepseek-reasoner",
            description="API请求的模型名称，默认为 deepseek-reasoner ",
        )

    def __init__(self):
        self.valves = self.Valves()
        self.data_prefix = "data:"
        self.emitter = None

    def pipes(self):
        models = self.valves.DEEPSEEK_API_MODEL.split(",")
        return [
            {
                "id": model.strip(),
                "name": model.strip(),
            }
            for model in models
        ]

    async def pipe(
        self,
        body: dict,
        __event_emitter__: Callable[[dict], Awaitable[None]] | Any = None,
    ) -> AsyncGenerator[str]:
        """主处理管道（已移除缓冲）"""
        print("-------------------------------------------------------------------------")
        self.emitter = __event_emitter__
        VALIDATED_PARAMETERS = [
            "stream",
            # "presence_penalty",
            # "frequency_penalty",
            # "repetition_penalty",
            "temperature",
            "top_p",
            # "top_k",
            # "seed",
            # "stop",
            # "stop_token_ids",
            # "include_stop_str_in_output",
            # "skip_special_tokens",
            # "ignore_eos",
            "max_tokens",
            # "reasoning_content",
            "num_context",
        ]
        # 验证配置
        if not self.valves.DEEPSEEK_API_KEY:
            yield json.dumps({"error": "未配置API密钥"}, ensure_ascii=False)
            return
        # 准备请求参数
        headers = {
            "Authorization": f"Bearer {self.valves.DEEPSEEK_API_KEY}",
            "Content-Type": "application/json",
        }
        try:
            # 模型ID提取
            model_id = body["model"].split(".", 1)[-1]
            payload = {**body, "model": model_id}
            # 处理来自codecompanion的消息
            codecompanion_messages = payload.get("system", [])
            for codecompanion_message in codecompanion_messages:
                if not isinstance(codecompanion_message, dict):
                    continue
                msg = codecompanion_message.get("text")
                if not msg:
                    continue
                sys_msg = {"role": "system", "content": msg}
                payload["messages"].insert(0, sys_msg)
            if "system" in payload:
                del payload["system"]
            codecompanion_options = payload.get("options", {})
            # 过滤无效的设置
            for option_key, option_value in codecompanion_options.items():
                if (option_key not in payload.keys()) and (option_key in VALIDATED_PARAMETERS):
                    payload[option_key] = option_value
            if "options" in payload:
                del payload["options"]
            # payload["stream_options"] = {"include_usage": True}
            # DeepSeek-R1不建议使用 system prompts
            for i, message in enumerate(payload["messages"]):
                if message["role"] == "system":
                    payload["messages"][i]["role"] = "user"

            # 处理消息以防止连续的相同角色
            messages = payload["messages"]
            i = 0
            while i < len(messages) - 1:
                if messages[i]["role"] == messages[i + 1]["role"]:
                    # 插入具有替代角色的占位符消息
                    alternate_role = "assistant" if messages[i]["role"] == "user" else "user"
                    messages.insert(
                        i + 1,
                        {"role": alternate_role, "content": "[Unfinished thinking]"},
                    )
                i += 1
            use_stream = payload.get("stream", True)
            first_content_fetched = False
            # 发起API请求
            async with httpx.AsyncClient(http2=True) as client:
                async with client.stream(
                    "POST",
                    f"{self.valves.DEEPSEEK_API_BASE_URL}/chat/completions",
                    json=payload,
                    headers=headers,
                    timeout=300,
                ) as response:
                    # 错误处理
                    if response.status_code != 200:
                        error = await response.aread()
                        yield self._format_error(response.status_code, error)
                        return

                    # 流式处理响应
                    async for line in response.aiter_lines():
                        if not use_stream:
                            json_str = line.strip()
                        else:
                            if not line.startswith(self.data_prefix):
                                continue

                            # 截取 JSON 字符串
                            json_str = line[len(self.data_prefix) :].strip()

                        # 去除首尾空格后检查是否为结束标记
                        if json_str == "[DONE]":
                            return
                        try:
                            data = json.loads(json_str)
                        except json.JSONDecodeError as e:
                            error_detail = f"解析失败 - 内容：{json_str}，原因：{e}"
                            yield self._format_error("JSONDecodeError", error_detail)
                            return
                        # 方案 A: 检查 choices 是否存在且非空
                        choices = data.get("choices")
                        if not choices or len(choices) == 0:
                            continue  # 跳过没有 choices 的数据块
                        choice = choices[0]
                        # 如果不使用流式回答，则一次性返回结果。
                        if not use_stream:
                            content = choice.get("message", {}).get("content", "")
                            if (not first_content_fetched) and (not content.startswith("<think>")):
                                first_content_fetched = True
                                content = "<think>\n" + content
                                yield content
                                return
                        # 如果使用流式回答，流式返回结果。
                        elif use_stream:
                            # 结束条件判断
                            if choice.get("finish_reason"):
                                return
                            # 处理并立即发送内容
                            content = self._process_content(choice["delta"])
                            if content:
                                # 处理思考状态标记
                                # fix for huarun
                                if (not first_content_fetched) and (not content.startswith("<think>")):
                                    first_content_fetched = True
                                    yield "<think>"
                                    await asyncio.sleep(0.1)
                                    yield "\n"
                                # end fix for huarun
                                elif content.startswith("<think>"):
                                    content = re.sub(r"^<think>", "", content)
                                    yield "<think>"
                                    await asyncio.sleep(0.1)
                                    yield "\n"
                                elif content.startswith("</think>"):
                                    content = re.sub(r"^</think>", "", content)
                                    yield "</think>"
                                    await asyncio.sleep(0.1)
                                    yield "\n"
                                yield content
        except Exception as e:
            yield self._format_exception(e)

    def _process_content(self, delta: dict) -> str:
        """直接返回处理后的内容"""
        return delta.get("reasoning_content", "") or delta.get("content", "")

    def _emit_status(self, description: str, done: bool = False) -> Awaitable[None]:
        """发送状态更新"""
        if self.emitter:
            return self.emitter(
                {
                    "type": "status",
                    "data": {
                        "description": description,
                        "done": done,
                    },
                }
            )
        return None  # pyright: ignore

    def _format_error(self, status_code: int | str, error: bytes | str) -> str:
        if isinstance(error, str):
            error_str = error
        else:
            error_str = error.decode(errors="ignore")
        try:
            err_msg = json.loads(error_str).get("message", error_str)[:200]
        except Exception:
            err_msg = error_str[:200]
        return json.dumps({"error": f"HTTP {status_code}: {err_msg}"}, ensure_ascii=False)

    def _format_exception(self, e: Exception) -> str:
        tb_lines = traceback.format_exception(type(e), e, e.__traceback__)
        detailed_error = "".join(tb_lines)
        return json.dumps({"error": detailed_error}, ensure_ascii=False)
