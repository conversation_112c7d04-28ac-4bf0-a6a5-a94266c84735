"""
使用示例：如何使用AI API代理
"""

import asyncio
import json
import os

import httpx


async def test_proxy_with_real_api():
    """测试代理与真实API的交互"""
    
    # 注意：这需要真实的API密钥才能工作
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("⚠️  需要设置 DEEPSEEK_API_KEY 环境变量才能测试真实API")
        return
    
    # 代理服务器地址（假设在8000端口运行）
    proxy_url = "http://localhost:8000"
    
    # 测试请求
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "deepseek-reasoner",
        "messages": [
            {"role": "user", "content": "请简单介绍一下你自己"}
        ],
        "stream": True,
        "max_tokens": 100
    }
    
    print("🚀 测试代理服务器...")
    print(f"代理地址: {proxy_url}")
    print("-" * 50)
    
    try:
        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST",
                f"{proxy_url}/chat/completions",
                json=payload,
                headers=headers,
                timeout=30
            ) as response:
                print(f"响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    print("📝 流式响应内容:")
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_content = line[6:].strip()
                            if data_content == "[DONE]":
                                break
                            try:
                                data = json.loads(data_content)
                                choices = data.get("choices", [])
                                if choices:
                                    delta = choices[0].get("delta", {})
                                    content = delta.get("content", "") or delta.get("reasoning_content", "")
                                    if content:
                                        print(content, end="", flush=True)
                            except json.JSONDecodeError:
                                continue
                    print("\n")
                else:
                    error_content = await response.aread()
                    print(f"❌ 错误: {error_content.decode()}")
                    
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        print("请确保代理服务器正在运行: dsf --port 8000")


def show_usage_examples():
    """显示使用示例"""
    print("🔧 AI API代理使用指南")
    print("=" * 50)
    
    print("\n1. 启动代理服务器:")
    print("   # 使用默认设置")
    print("   dsf")
    print()
    print("   # 指定目标API和端口")
    print("   dsf --target-url https://api.deepseek.com/v1 --port 8000")
    print()
    print("   # 使用环境变量")
    print("   export TARGET_API_URL=https://your-api-endpoint.com/v1")
    print("   dsf --port 8000")
    
    print("\n2. 在你的应用中使用:")
    print("   # 原来的API地址")
    print("   # https://api.deepseek.com/v1/chat/completions")
    print("   ")
    print("   # 改为代理地址")
    print("   # http://localhost:8000/chat/completions")
    
    print("\n3. 功能说明:")
    print("   ✅ 完全透传所有HTTP请求和响应")
    print("   ✅ 自动在第一个内容前添加 <think> 标签（如果缺失）")
    print("   ✅ 支持流式和非流式响应")
    print("   ✅ 保持所有原始headers和状态码")
    
    print("\n4. 环境变量:")
    print("   TARGET_API_URL - 目标API的基础URL")
    print("   DEEPSEEK_API_KEY - 用于测试的API密钥")
    
    print("\n5. 测试代理:")
    print("   python example_usage.py")


async def main():
    """主函数"""
    show_usage_examples()
    
    print("\n" + "=" * 50)
    await test_proxy_with_real_api()


if __name__ == "__main__":
    asyncio.run(main())
