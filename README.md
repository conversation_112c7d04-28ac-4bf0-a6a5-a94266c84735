# DSF - AI API转发代理

一个简单的FastAPI转发工具，用于修复AI模型API缺少`<think>`标签的问题。

## 功能特点

- **透明代理**: 完全透传所有HTTP请求和响应
- **自动修复**: 在第一个返回内容前自动添加`<think>`标签（如果缺失）
- **流式支持**: 支持流式和非流式响应
- **简单配置**: 最小化配置，专注核心功能

## 安装

```bash
pip install -e .
```

> **注意**: 如果遇到安装问题，可以直接运行 `python -m dsf.main` 来启动服务器。

## 使用方法

### 命令行启动

```bash
# 使用默认设置（代理到 https://api.deepseek.com/v1）
dsf

# 指定目标API
dsf --target-url https://your-api-endpoint.com/v1

# 自定义端口和地址
dsf --host 0.0.0.0 --port 8080

# 开发模式（自动重载）
dsf --reload
```

### 环境变量配置

```bash
export TARGET_API_URL=https://your-api-endpoint.com/v1
dsf
```

### 程序化使用

```python
from dsf import create_app
import uvicorn

# 创建应用
app = create_app("https://your-api-endpoint.com/v1")

# 启动服务器
uvicorn.run(app, host="127.0.0.1", port=8000)
```

## 工作原理

1. **请求转发**: 接收所有HTTP请求，完整转发到目标API
2. **响应处理**: 
   - 对于流式响应：检查第一个内容块，如果不以`<think>`开头则自动添加
   - 对于非流式响应：检查JSON内容，修复缺失的`<think>`标签
3. **透明代理**: 除了添加`<think>`标签外，所有其他内容保持不变

## 示例

启动代理服务器：
```bash
dsf --target-url https://api.deepseek.com/v1 --port 8000
```

然后将你的应用指向代理地址：
```
http://localhost:8000/chat/completions
```

代理会自动转发到：
```
https://api.deepseek.com/v1/chat/completions
```

并在响应中添加缺失的`<think>`标签。

## 参数说明

- `--target-url`: 目标API的基础URL
- `--host`: 监听地址（默认: 127.0.0.1）
- `--port`: 监听端口（默认: 8000）
- `--reload`: 启用自动重载（开发模式）

## 环境变量

- `TARGET_API_URL`: 目标API的基础URL（如果未通过命令行指定）
