"""
AI API转发代理服务器
用于修复缺少<think>标签的AI模型API响应
"""

import json
import os
from typing import Any

import httpx
from fastapi import FastAPI, Request, Response
from fastapi.responses import StreamingResponse


class AIProxy:
    def __init__(self, target_url: str):
        self.target_url = target_url.rstrip("/")
        # 优化httpx客户端配置以提高流式性能
        try:
            # 尝试启用HTTP/2支持
            self.client = httpx.AsyncClient(
                verify=False,
                timeout=300.0,
                limits=httpx.Limits(max_keepalive_connections=20, max_connections=100),
                http2=True,
            )
        except ImportError:
            # 如果没有h2包，则使用HTTP/1.1
            self.client = httpx.AsyncClient(
                verify=False, timeout=300.0, limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
            )

    async def forward_request(self, request: Request, path: str) -> Response:
        """转发请求到目标API"""
        # 构建目标URL
        target_url = f"{self.target_url}/{path.lstrip('/')}"
        if request.url.query:
            target_url += f"?{request.url.query}"

        # 获取请求体
        body = await request.body()

        # 准备headers（排除host等）
        headers = dict(request.headers)
        headers.pop("host", None)
        headers.pop("content-length", None)

        # 检查是否是流式请求
        is_stream_request = False
        try:
            if body:
                request_data = json.loads(body)
                is_stream_request = request_data.get("stream", False)
        except json.JSONDecodeError:
            pass

        print(f"开始发送请求到: {target_url}")

        # 根据是否是流式请求选择不同的处理方式
        if is_stream_request:
            # 流式请求：使用client.stream()直接处理，参考example.py的实现
            return await self._handle_stream_request(target_url, request.method, headers, body)
        else:
            # 非流式请求：使用普通request
            response = await self.client.request(
                method=request.method, url=target_url, headers=headers, content=body, follow_redirects=True
            )
            print(f"收到响应，状态码: {response.status_code}")
            return await self._handle_non_stream_response(response)

    async def _handle_stream_request(self, target_url: str, method: str, headers: dict, body: bytes) -> Response:
        """处理流式请求，使用client.stream()优化性能"""

        # 使用client.stream()直接处理流式请求，参考example.py的实现
        async def stream_generator():
            first_content_sent = False

            async with self.client.stream(
                method=method,
                url=target_url,
                headers=headers,
                content=body,
                timeout=300,
            ) as response:
                print(f"收到响应，状态码: {response.status_code}")

                # 错误处理
                if response.status_code != 200:
                    error = await response.aread()
                    yield f"data: {json.dumps({'error': error.decode()})}\n\n"
                    return

                # 流式处理响应
                async for line in response.aiter_lines():
                    if not line.strip():
                        yield "\n"
                        continue

                    # 处理SSE格式
                    if line.startswith("data: "):
                        data_content = line[6:].strip()
                        if data_content == "[DONE]":
                            yield f"{line}\n\n"
                            return

                        try:
                            data = json.loads(data_content)
                            # 检查是否需要添加<think>标签
                            if not first_content_sent:
                                data = self._add_think_tag_to_first_chunk(data)
                                if self._has_content(data):
                                    first_content_sent = True

                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                        except json.JSONDecodeError:
                            # 如果解析失败，直接转发原始行
                            yield f"{line}\n\n"
                    else:
                        # 处理其他SSE字段（如event:, id:等）
                        yield f"{line}\n"

        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # 禁用nginx缓冲
            },
        )

    async def _handle_non_stream_response(self, response: httpx.Response) -> Response:
        """处理非流式响应"""
        content = response.content
        headers = self._filter_response_headers(dict(response.headers))
        content_type = response.headers.get("content-type", "")

        if content_type.startswith("application/json"):
            try:
                data = json.loads(content)
                modified_data = self._fix_json_response(data)
                content = json.dumps(modified_data, ensure_ascii=False).encode()
                # 更新Content-Length
                headers["content-length"] = str(len(content))
            except json.JSONDecodeError:
                pass

        return Response(content=content, status_code=response.status_code, headers=headers)

    async def _process_stream(self, response: httpx.Response):
        """处理流式响应，添加<think>标签"""
        first_content_sent = False

        async for line in response.aiter_lines():
            # 保持原始格式，包括空行
            if not line.strip():
                yield "\n"
                continue

            # 处理SSE格式
            if line.startswith("data: "):
                data_content = line[6:].strip()
                if data_content == "[DONE]":
                    yield f"{line}\n\n"
                    break

                try:
                    data = json.loads(data_content)
                    # 检查是否需要添加<think>标签
                    if not first_content_sent:
                        data = self._add_think_tag_to_first_chunk(data)
                        if self._has_content(data):
                            first_content_sent = True

                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                except json.JSONDecodeError:
                    # 如果解析失败，直接转发原始行
                    yield f"{line}\n\n"
            else:
                # 处理其他SSE字段（如event:, id:等）
                yield f"{line}\n"

    def _add_think_tag_to_first_chunk(self, data: dict[str, Any]) -> dict[str, Any]:
        """在第一个内容块添加<think>标签"""
        choices = data.get("choices", [])
        if not choices:
            return data

        choice = choices[0]
        delta = choice.get("delta", {})

        # 检查content字段
        content = delta.get("content", "")
        reasoning_content = delta.get("reasoning_content", "")

        # 如果有内容且不以<think>开头，则添加
        if content and not content.startswith("<think>"):
            delta["content"] = "<think>\n" + content
        elif reasoning_content and not reasoning_content.startswith("<think>"):
            delta["reasoning_content"] = "<think>\n" + reasoning_content

        return data

    def _fix_json_response(self, data: dict[str, Any]) -> dict[str, Any]:
        """修复非流式JSON响应"""
        choices = data.get("choices", [])
        if not choices:
            return data

        choice = choices[0]
        message = choice.get("message", {})
        content = message.get("content", "")

        if content and not content.startswith("<think>"):
            message["content"] = "<think>\n" + content

        return data

    def _has_content(self, data: dict[str, Any]) -> bool:
        """检查数据块是否包含实际内容"""
        choices = data.get("choices", [])
        if not choices:
            return False

        delta = choices[0].get("delta", {})
        return bool(delta.get("content") or delta.get("reasoning_content"))

    def _filter_response_headers(self, headers: dict[str, str]) -> dict[str, str]:
        """过滤响应头，避免与FastAPI自动添加的headers冲突，并保持原生API的header格式"""
        # 标准化header名称映射（保持与原生API一致的大小写）
        header_name_mapping = {
            "content-type": "Content-Type",
            "transfer-encoding": "Transfer-Encoding",
            "connection": "Connection",
            "cache-control": "Cache-Control",
            "x-oneapi-request-id": "X-Oneapi-Request-Id",
            "server": "Server",
            "date": "Date",
        }

        # 标准化所有headers，保持原生API的格式
        filtered = {}
        for key, value in headers.items():
            key_lower = key.lower()
            # 使用标准化的header名称，如果没有映射则保持原样
            standard_key = header_name_mapping.get(key_lower, key)
            filtered[standard_key] = value

        return filtered


def create_app(target_url: str | None = None) -> FastAPI:
    """创建FastAPI应用"""
    if not target_url:
        target_url = os.getenv("TARGET_API_URL", "https://api.deepseek.com/v1")

    app = FastAPI(title="AI API Proxy", description="转发AI模型API并修复<think>标签")
    proxy = AIProxy(target_url)

    @app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"])
    async def proxy_request(request: Request, path: str):  # type: ignore
        """代理所有请求"""
        return await proxy.forward_request(request, path)

    return app
